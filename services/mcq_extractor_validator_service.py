"""
Service for extracting and validating MCQs from PDF resources.
"""

import logging
import asyncio
import traceback
import uuid
import requests
from typing import Dict, Any

import config
from agents.mcq_extractor import MCQExtractor
from agents.mcq_validator import MCQValidator
from db_config.db import get_session, CONTENT_SCHEMA
from sqlalchemy import text

# Get logger instance
logger = logging.getLogger(__name__)

class MCQExtractorValidatorService:
    """
    Service for extracting and validating MCQs from PDF resources.
    """

    async def extract_and_validate_mcqs(self, res_id: str, username: str) -> Dict[str, Any]:
        """
        Extract and validate MCQs from a PDF resource.

        Args:
            res_id: Resource ID
            username: Username of the user performing the extraction

        Returns:
            Dict: Validation results with MCQs from the database
        """
        # Generate a unique request ID for logging
        request_id = str(uuid.uuid4())
        logger.info(f"[REQUEST:{request_id}] Starting MCQ extraction and validation for resource ID {res_id}")

        try:
            # Step 1: Extract MCQs
            logger.info(f"[REQUEST:{request_id}] Step 1: Extracting MCQs from resource ID {res_id}")
            extraction_result = await self._extract_mcqs(res_id, request_id, username)

            if extraction_result.get("status") != "success":
                logger.error(f"[REQUEST:{request_id}] MCQ extraction failed: {extraction_result.get('message')}")
                return {"status": "error", "message": f"MCQ extraction failed: {extraction_result.get('message')}"}

            # Check if there were any skipped extractions due to 504 errors
            skipped_count = extraction_result.get("skipped_count", 0)
            if skipped_count > 0:
                skipped_locations = extraction_result.get("skipped_locations", [])
                logger.warning(f"[REQUEST:{request_id}] {skipped_count} extractions were skipped due to 504 errors: {', '.join(skipped_locations)}")

            # Step 2: Get quiz_id
            quiz_id = await self._get_quiz_id_from_database(extraction_result["process_result"]["quiz_id"], request_id)
            chapter_id = extraction_result["process_result"]["chapter_id"]
            book_id = extraction_result["process_result"]["book_id"]

            if not quiz_id:
                logger.error(f"[REQUEST:{request_id}] Failed to retrieve quiz ID from extraction result or database")
                return {"status": "error", "message": "Failed to retrieve quiz ID from extraction result or database"}

            # Step 3: Validate MCQs
            logger.info(f"[REQUEST:{request_id}] Step 3: Validating MCQs for quiz ID {quiz_id}")
            validation_results = await self._validate_mcqs(quiz_id, request_id)
            validation_results["quiz_id"] = extraction_result["process_result"]["quiz_id"]

            # Include information about skipped extractions in the validation results
            skipped_count = extraction_result.get("skipped_count", 0)
            if skipped_count > 0:
                skipped_locations = extraction_result.get("skipped_locations", [])
                validation_results["skipped_extractions"] = {
                    "count": skipped_count,
                    "locations": skipped_locations
                }

            # Step 4: Call external API to update mcqs_extracted flag
            if chapter_id:
                logger.info(f"[REQUEST:{request_id}] Step 4: Calling external API to update mcqs_extracted flag for chapter ID {chapter_id}")
                api_result = await self._call_mcqs_extracted_api(chapter_id, request_id)
                if api_result:
                    logger.info(f"[REQUEST:{request_id}] Successfully updated mcqs_extracted flag for chapter ID {chapter_id}")
                else:
                    logger.warning(f"[REQUEST:{request_id}] Failed to update mcqs_extracted flag for chapter ID {chapter_id}")
            else:
                logger.warning(f"[REQUEST:{request_id}] Chapter ID not found, skipping mcqs_extracted flag update")

            from agents.task_manager import delete_folder_by_id

            if delete_folder_by_id(book_id, chapter_id, res_id):
                logger.info(f"Successfully deleted resource folder for res_id: {res_id}")
            else:
                logger.warning(f"Failed to delete resource folder for res_id: {res_id}")

            # Return the validation results
            logger.info(f"[REQUEST:{request_id}] MCQ extraction and validation completed successfully")
            return validation_results

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error in MCQ extraction and validation: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": f"Error in MCQ extraction and validation: {str(e)}"}

    async def _extract_mcqs(self, res_id: str, request_id: str, username: str) -> Dict[str, Any]:
        """
        Extract MCQs from a PDF resource.

        Args:
            res_id: Resource ID
            request_id: Unique request ID for logging

        Returns:
            Dict: Extraction results
        """
        try:
            mcq_extractor = MCQExtractor()  # Use default config values for memory optimization
            status_result = await mcq_extractor.check_resource_processed(res_id)

            if status_result['status'] == 'processed' and status_result.get('file_count', 0) > 0:
                logger.info(f"[REQUEST:{request_id}] Resource already processed with {status_result['file_count']} files")
                logger.info(f"[REQUEST:{request_id}] Proceeding with extraction to get structured results")

            # Use a timeout to ensure we don't wait forever
            try:
                # Try to run the extraction with a timeout
                extraction_result = await asyncio.wait_for(
                    mcq_extractor.extract_mcqs_from_resource(res_id=res_id, username=username),
                    timeout=2700  # 45 minutes (reduced from 2 hours for better resource management)
                )
                return extraction_result

            except asyncio.TimeoutError:
                logger.error(f"[REQUEST:{request_id}] MCQ extraction timed out after 30 minutes")
                return {"status": "error", "message": "MCQ extraction timed out after 30 minutes"}

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error extracting MCQs: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": f"Error extracting MCQs: {str(e)}"}

    async def _get_quiz_id(self, res_id: str, extraction_result: Dict[str, Any], request_id: str) -> str:
        """
        Get the quiz_id from the extraction result or database.

        Args:
            res_id: Resource ID
            extraction_result: Extraction results
            request_id: Unique request ID for logging

        Returns:
            str: Quiz ID or None if not found
        """
        # Try to get the quiz_id from the processed MCQs
        # The quiz_id is returned in the merge_result.processed_mcqs[0].additionalInformation.resId
        quiz_id = None
        if extraction_result.get("merge_result") and extraction_result["merge_result"].get("processed_mcqs"):
            processed_mcqs = extraction_result["merge_result"]["processed_mcqs"]
            if processed_mcqs and len(processed_mcqs) > 0:
                first_mcq = processed_mcqs[0]
                if first_mcq.get("additionalInformation") and first_mcq["additionalInformation"].get("resId"):
                    quiz_id = first_mcq["additionalInformation"]["resId"]
                    logger.info(f"[REQUEST:{request_id}] Got quiz_id {quiz_id} from processed MCQs")

        # If we couldn't get the quiz_id from the processed MCQs, try to get it from the database
        if not quiz_id:
            logger.info(f"[REQUEST:{request_id}] Trying to get quiz_id from database")
            quiz_id = await self._get_quiz_id_from_database(res_id, request_id)

        return quiz_id

    async def _get_quiz_id_from_database(self, res_id: str, request_id: str) -> str:
        """
        Get the quiz_id from the database.

        Args:
            res_id: Resource ID
            request_id: Unique request ID for logging

        Returns:
            str: Quiz ID or None if not found
        """
        # Get a database session for the wscontent schema
        db_session = next(get_session(CONTENT_SCHEMA))
        try:
            # Query the resource_dtl table to get resource details
            resource_query = text("""
                SELECT id, chapter_id, res_link, resource_name
                FROM wscontent.resource_dtl
                WHERE id = :res_id AND res_type = 'Multiple Choice Questions'
                LIMIT 1
            """)

            resource_result = db_session.execute(resource_query, {"res_id": res_id})
            resource_row = resource_result.fetchone()

            if not resource_row:
                logger.warning(f"[REQUEST:{request_id}] No MCQ resource found with ID: {res_id}")
                return None

            # Extract resource details
            resource_id = resource_row[0]
            chapter_id = resource_row[1]
            quiz_id = resource_row[2]  # res_link contains the quiz_id

            logger.info(f"[REQUEST:{request_id}] Got quiz_id {quiz_id} from database")
            return quiz_id
        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error getting quiz_id from database: {str(e)}")
            logger.error(traceback.format_exc())
            return None
        finally:
            # Close the database session immediately after retrieving data
            db_session.close()

    async def _validate_mcqs(self, quiz_id: str, request_id: str) -> Dict[str, Any]:
        """
        Validate MCQs using the MCQValidator.

        Args:
            quiz_id: Quiz ID
            request_id: Unique request ID for logging

        Returns:
            Dict: Validation results
        """
        try:
            validator = MCQValidator()
            validation_results = await validator.validate_mcqs(quiz_id)
            return validation_results
        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error validating MCQs: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": f"Error validating MCQs: {str(e)}"}

    async def _call_mcqs_extracted_api(self, chapter_id: str, request_id: str) -> bool:
        """
        Call the external API to update the mcqs_extracted flag.

        Args:
            chapter_id: Chapter ID
            request_id: Unique request ID for logging

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the base URL from config or use a default
            base_url = getattr(config, 'BASE_URL', 'http://localhost:8000')

            # Construct the API URL
            api_url = f"{base_url}/pdfExtractor/mcqsExtracted?chapterId={chapter_id}"

            # Log the API call
            logger.info(f"[REQUEST:{request_id}] Calling API: {api_url}")

            # Call the API
            response = requests.get(api_url)

            # Check the response
            if response.status_code == 200:
                response_data = response.json()
                if response_data == "success":
                    logger.info(f"[REQUEST:{request_id}] API call successful: {response_data}")
                    return True
                else:
                    logger.warning(f"[REQUEST:{request_id}] API call returned unexpected response: {response_data}")
                    return False
            else:
                logger.error(f"[REQUEST:{request_id}] API call failed with status code {response.status_code}: {response.text}")
                return False
        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error calling mcqs_extracted API: {str(e)}")
            logger.error(traceback.format_exc())
            return False
